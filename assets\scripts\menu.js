cc.Class({
    extends: cc.Component,

    properties: {
        button: cc.Node
    },

    // LIFE-CYCLE CALLBACKS:

     onLoad () {
        this.test();
        this.button.on('click', this.onStartBtn, this);
     },

    start () {

    },

    // update (dt) {},

    // 添加一个函数，用于处理按钮点击之后所要做的事情
    onStartBtn(event) {
        // console.log("点击按钮了！");
        console.log(event);
        // console.log(parseInt(id));
        // string类型转换成number
        // parseInt();
        // 进入到游戏场景
        cc.director.loadScene("game");
    },

    test() {
        console.log("测试！");
    }
});
